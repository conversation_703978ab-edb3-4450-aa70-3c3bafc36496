# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is Claro, a customs automation platform built as a Rush monorepo. The system processes trade documents, manages customs filings, and provides automated compliance checking for import/export operations.

**Main Applications:**
- `portal` - Customer-facing React frontend
- `portal-api` - Main NestJS backend API with LLM integration
- `backoffice` - Admin React frontend  
- `backoffice-api` - Admin NestJS backend
- `bullmq-board` - Queue monitoring dashboard
- `cloud-functions` - Firebase cloud functions

**Shared Libraries:**
- `libraries/nest-modules` - Shared NestJS modules, entities, DTOs, and services
- `libraries/ui` - Shared React UI components
- `tools/utils` - Shared utilities

## Common Commands

**Development Setup:**
```bash
# Install packages (use Rush, never npm install directly)
rush update

# Build all projects
rush build

# Start development servers
rush dev                    # All services in parallel
rush fe                     # Frontend portal only  
rush start -t portal        # Specific project
rush start -t portal-api    # API server with watch mode

# Start supporting services (Redis, Postgres, etc.)
rush services
```

**Testing:**
```bash
# Run tests in portal-api (has Jest configured)
cd apps/portal-api && rushx jest

# Check TypeScript in frontend apps
cd apps/portal && rushx lint
```

**Database Operations:**
```bash
# Generate migration (from portal-api directory)
cd apps/portal-api && npx typeorm migration:generate src/migrations/MigrationName -d src/data-source.ts

# Run migrations
cd apps/portal-api && npx typeorm migration:run -d src/data-source.ts
```

## Architecture Overview

**Backend (NestJS):**
- Event-driven architecture with `EventEmitter2`
- BullMQ for background job processing
- TypeORM with PostgreSQL database
- Multi-tenant with organization scoping via `ClsService`
- LLM integration for document processing and AI features

**Frontend (React + TypeScript):**
- Vite build system with React + SWC
- MobX for state management
- TanStack Query for server state
- Tailwind CSS for styling
- React Router for navigation

**Key Backend Patterns:**
- Use `@Scope(Scope.REQUEST)` and `ClsService` for multi-tenancy
- Inject `EventEmitter2` for event publishing
- Use `@OnEvent()` decorators for event listeners
- Background jobs with `@Processor()` classes extending `WorkerHost`
- LLM calls through `AskLLMService` with typed schemas

**Database:**
- All entities extend `BaseEntity` with common fields (id, createdAt, updatedAt, organizationId)
- Use scoping helpers like `inOrgCls()` for tenant isolation
- TypeORM repositories with `getFindOptions()` helper for standardized querying

## Key Domain Models

**Core Entities:**
- `Organization` - Multi-tenant root entity
- `User` - User accounts with role-based access
- `TradePartner` - Import/export business partners
- `Shipment` - Cargo shipments being processed
- `Document` - Trade documents (invoices, bills of lading, etc.)
- `DocumentAggregation` - Grouped documents for customs filing
- `Email` - Processed email communications
- `Product` - Trade goods with HS codes and compliance data

**Compliance & Filing:**
- `OgdFiling` - Canadian customs filings
- `SimaFiling` - Anti-dumping/countervailing duty filings
- `MatchingRule` - Business rules for automated processing
- `UsTariff`/`CanadaTariff` - Tariff code databases

## Development Guidelines

**Code Standards:**
- Follow functional programming patterns with pure functions
- Use TypeScript with explicit typing
- Implement JSDoc for functions over 10 lines
- Prefer composition over inheritance
- Use method chaining with Array methods (map, filter, reduce)

**NestJS Patterns:**
- Use path aliases `@/*` for imports
- Group imports: NestJS modules, project modules, external libraries
- Include Swagger decorators (`@ApiOperation`, `@ApiOkResponse`)
- Use dependency injection for services, repositories, queues
- Handle async operations with async/await
- Throw standard NestJS exceptions (`NotFoundException`, `BadRequestException`)

**Database Patterns:**
- Use TypeORM repositories and QueryRunner for transactions
- Apply organization scoping with `inOrgCls()` helper
- Use `getFindOptions()` for standardized queries
- Follow migration naming: `TIMESTAMP-DescriptiveName.ts`

**Frontend Patterns:**
- Use MobX stores for client state
- TanStack Query for server state with error boundaries
- Form validation with Formik + Yup
- Consistent component structure in modules (Types, Schema, Routes)

**Rush Monorepo Rules:**
- NEVER use `npm install` - always use `rush add -p package-name`
- Use `rush update` after adding dependencies
- Use `rushx command` to run package scripts from project directories
- Use `rush start -t project-name` to start specific projects

## Testing

- Tests are configured in `portal-api` with Jest
- Use `rushx jest` from the portal-api directory
- Test files follow pattern `*.spec.ts`
- Mock external dependencies and use request-scoped testing where needed

## Important Notes

- This is a multi-tenant system - always consider organization scoping
- Email processing is a core feature with complex parsing and aggregation
- LLM integration is used extensively for document understanding
- Queue processing handles heavy background tasks
- Database migrations must be run in development and production environments
- The system processes sensitive trade and customs data - follow security best practices